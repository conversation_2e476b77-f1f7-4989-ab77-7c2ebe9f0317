package main

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 数据库配置
	dbHost := "**************"
	dbPort := "3306"
	dbUser := "root"
	dbPassword := "d933af3930d9bc45"
	dbName := "temp_email"

	// 构建连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		dbUser, dbPassword, dbHost, dbPort, dbName)

	fmt.Printf("🔍 数据库检查工具\n")
	fmt.Printf("================\n")
	fmt.Printf("数据库: %s@%s:%s/%s\n", dbUser, dbHost, dbPort, dbName)
	fmt.Printf("\n")

	// 连接数据库
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("❌ 数据库连接失败: %v", err)
	}
	defer db.Close()

	// 测试连接
	err = db.Ping()
	if err != nil {
		log.Fatalf("❌ 数据库ping失败: %v", err)
	}

	fmt.Printf("✅ 数据库连接成功\n\n")

	// 检查邮件表
	fmt.Printf("📊 邮件统计信息:\n")
	fmt.Printf("================\n")

	// 总邮件数
	var totalEmails int
	err = db.QueryRow("SELECT COUNT(*) FROM emails").Scan(&totalEmails)
	if err != nil {
		log.Printf("❌ 查询总邮件数失败: %v", err)
	} else {
		fmt.Printf("总邮件数: %d\n", totalEmails)
	}

	// 今日邮件数
	var todayEmails int
	err = db.QueryRow("SELECT COUNT(*) FROM emails WHERE DATE(received_at) = CURDATE()").Scan(&todayEmails)
	if err != nil {
		log.Printf("❌ 查询今日邮件数失败: %v", err)
	} else {
		fmt.Printf("今日邮件数: %d\n", todayEmails)
	}

	// 最近邮件数（最近1小时）
	var recentEmails int
	err = db.QueryRow("SELECT COUNT(*) FROM emails WHERE received_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)").Scan(&recentEmails)
	if err != nil {
		log.Printf("❌ 查询最近邮件数失败: %v", err)
	} else {
		fmt.Printf("最近1小时邮件数: %d\n", recentEmails)
	}

	// 特定邮箱的邮件数
	targetEmail := "<EMAIL>"
	var targetEmails int
	err = db.QueryRow("SELECT COUNT(*) FROM emails WHERE to_address = ?", targetEmail).Scan(&targetEmails)
	if err != nil {
		log.Printf("❌ 查询特定邮箱邮件数失败: %v", err)
	} else {
		fmt.Printf("邮箱 %s 的邮件数: %d\n", targetEmail, targetEmails)
	}

	fmt.Printf("\n")

	// 显示最近的邮件
	fmt.Printf("📧 最近的邮件记录:\n")
	fmt.Printf("==================\n")

	rows, err := db.Query(`
		SELECT id, message_id, to_address, from_address, subject, received_at, size 
		FROM emails 
		ORDER BY received_at DESC 
		LIMIT 10
	`)
	if err != nil {
		log.Printf("❌ 查询最近邮件失败: %v", err)
	} else {
		defer rows.Close()

		fmt.Printf("%-5s %-20s %-25s %-25s %-30s %-20s %-8s\n", 
			"ID", "Message-ID", "收件人", "发件人", "主题", "接收时间", "大小")
		fmt.Printf("%s\n", "--------------------------------------------------------------------------------------------")

		for rows.Next() {
			var id int64
			var messageId, toAddr, fromAddr, subject string
			var receivedAt time.Time
			var size int

			err := rows.Scan(&id, &messageId, &toAddr, &fromAddr, &subject, &receivedAt, &size)
			if err != nil {
				log.Printf("❌ 扫描行失败: %v", err)
				continue
			}

			// 截断长字段
			if len(messageId) > 18 {
				messageId = messageId[:15] + "..."
			}
			if len(toAddr) > 23 {
				toAddr = toAddr[:20] + "..."
			}
			if len(fromAddr) > 23 {
				fromAddr = fromAddr[:20] + "..."
			}
			if len(subject) > 28 {
				subject = subject[:25] + "..."
			}

			fmt.Printf("%-5d %-20s %-25s %-25s %-30s %-20s %-8d\n",
				id, messageId, toAddr, fromAddr, subject, 
				receivedAt.Format("2006-01-02 15:04:05"), size)
		}
	}

	fmt.Printf("\n")

	// 检查表结构
	fmt.Printf("🏗️ 表结构信息:\n")
	fmt.Printf("==============\n")

	rows, err = db.Query("DESCRIBE emails")
	if err != nil {
		log.Printf("❌ 查询表结构失败: %v", err)
	} else {
		defer rows.Close()

		fmt.Printf("%-15s %-15s %-8s %-8s %-15s %-10s\n", 
			"字段", "类型", "空值", "键", "默认值", "额外")
		fmt.Printf("%s\n", "------------------------------------------------------------------------")

		for rows.Next() {
			var field, fieldType, null, key, defaultVal, extra sql.NullString

			err := rows.Scan(&field, &fieldType, &null, &key, &defaultVal, &extra)
			if err != nil {
				log.Printf("❌ 扫描表结构失败: %v", err)
				continue
			}

			fmt.Printf("%-15s %-15s %-8s %-8s %-15s %-10s\n",
				field.String, fieldType.String, null.String, key.String, 
				defaultVal.String, extra.String)
		}
	}

	fmt.Printf("\n✅ 数据库检查完成\n")
}
