package utils

import (
	"bufio"
	"fmt"
	"log"
	"net"
	"os"
	"os/exec"
	"runtime"
	"strconv"
	"strings"
	"time"
)

// PortManager 端口管理器
type PortManager struct{}

// NewPortManager 创建端口管理器
func NewPortManager() *PortManager {
	return &PortManager{}
}

// IsPortInUse 检查端口是否被占用
func (pm *PortManager) IsPortInUse(port int) bool {
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("localhost:%d", port), time.Second)
	if err != nil {
		return false
	}
	conn.Close()
	return true
}

// GetProcessByPort 根据端口获取占用的进程信息
func (pm *PortManager) GetProcessByPort(port int) (string, error) {
	var cmd *exec.Cmd
	
	switch runtime.GOOS {
	case "windows":
		cmd = exec.Command("netstat", "-ano")
	case "linux", "darwin":
		cmd = exec.Command("lsof", "-i", fmt.Sprintf(":%d", port))
	default:
		return "", fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}

	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("执行命令失败: %v", err)
	}

	return pm.parseProcessInfo(string(output), port)
}

// parseProcessInfo 解析进程信息
func (pm *PortManager) parseProcessInfo(output string, port int) (string, error) {
	lines := strings.Split(output, "\n")
	portStr := fmt.Sprintf(":%d", port)

	for _, line := range lines {
		if strings.Contains(line, portStr) && strings.Contains(line, "LISTEN") {
			fields := strings.Fields(line)
			if len(fields) > 0 {
				if runtime.GOOS == "windows" {
					// Windows netstat 输出格式
					if len(fields) >= 5 {
						pid := fields[len(fields)-1]
						processName, _ := pm.getProcessNameByPID(pid)
						return fmt.Sprintf("PID: %s, 进程: %s", pid, processName), nil
					}
				} else {
					// Linux/macOS lsof 输出格式
					if len(fields) >= 2 {
						return fmt.Sprintf("进程: %s, PID: %s", fields[0], fields[1]), nil
					}
				}
			}
		}
	}

	return "", fmt.Errorf("未找到占用端口 %d 的进程", port)
}

// getProcessNameByPID 根据PID获取进程名称
func (pm *PortManager) getProcessNameByPID(pid string) (string, error) {
	var cmd *exec.Cmd
	
	switch runtime.GOOS {
	case "windows":
		cmd = exec.Command("tasklist", "/FI", fmt.Sprintf("PID eq %s", pid), "/FO", "CSV", "/NH")
	case "linux", "darwin":
		cmd = exec.Command("ps", "-p", pid, "-o", "comm=")
	default:
		return "unknown", nil
	}

	output, err := cmd.Output()
	if err != nil {
		return "unknown", err
	}

	result := strings.TrimSpace(string(output))
	if runtime.GOOS == "windows" {
		// Windows tasklist CSV 格式解析
		if strings.Contains(result, ",") {
			parts := strings.Split(result, ",")
			if len(parts) > 0 {
				return strings.Trim(parts[0], "\""), nil
			}
		}
	}

	return result, nil
}

// KillProcessByPort 根据端口杀死占用的进程
func (pm *PortManager) KillProcessByPort(port int) error {
	var cmd *exec.Cmd
	
	switch runtime.GOOS {
	case "windows":
		// Windows: 先获取PID，再杀死进程
		netstatCmd := exec.Command("netstat", "-ano")
		output, err := netstatCmd.Output()
		if err != nil {
			return fmt.Errorf("获取进程信息失败: %v", err)
		}

		pid, err := pm.extractPIDFromNetstat(string(output), port)
		if err != nil {
			return err
		}

		cmd = exec.Command("taskkill", "/F", "/PID", pid)
	case "linux", "darwin":
		// Linux/macOS: 使用 lsof 和 kill
		lsofCmd := exec.Command("lsof", "-ti", fmt.Sprintf(":%d", port))
		pidOutput, err := lsofCmd.Output()
		if err != nil {
			return fmt.Errorf("获取进程PID失败: %v", err)
		}

		pid := strings.TrimSpace(string(pidOutput))
		if pid == "" {
			return fmt.Errorf("未找到占用端口 %d 的进程", port)
		}

		cmd = exec.Command("kill", "-9", pid)
	default:
		return fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}

	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("杀死进程失败: %v", err)
	}

	// 等待一下确保进程被杀死
	time.Sleep(time.Second)
	
	// 验证端口是否已释放
	if pm.IsPortInUse(port) {
		return fmt.Errorf("进程杀死后端口 %d 仍被占用", port)
	}

	return nil
}

// extractPIDFromNetstat 从netstat输出中提取PID
func (pm *PortManager) extractPIDFromNetstat(output string, port int) (string, error) {
	lines := strings.Split(output, "\n")
	portStr := fmt.Sprintf(":%d", port)

	for _, line := range lines {
		if strings.Contains(line, portStr) && strings.Contains(line, "LISTENING") {
			fields := strings.Fields(line)
			if len(fields) >= 5 {
				return fields[len(fields)-1], nil
			}
		}
	}

	return "", fmt.Errorf("未找到占用端口 %d 的进程PID", port)
}

// CheckAndHandlePortConflict 检查并处理端口冲突
func (pm *PortManager) CheckAndHandlePortConflict(port int, serviceName string, autoKill bool) error {
	if !pm.IsPortInUse(port) {
		return nil // 端口未被占用，无需处理
	}

	log.Printf("⚠️  端口 %d (%s) 已被占用", port, serviceName)

	// 获取占用进程信息
	processInfo, err := pm.GetProcessByPort(port)
	if err != nil {
		log.Printf("无法获取占用端口 %d 的进程信息: %v", port, err)
		processInfo = "未知进程"
	}

	log.Printf("占用信息: %s", processInfo)

	if autoKill {
		log.Printf("🔄 自动关闭占用端口 %d 的进程...", port)
		return pm.KillProcessByPort(port)
	}

	// 询问用户是否要关闭占用的进程
	fmt.Printf("\n端口 %d (%s) 被以下进程占用:\n", port, serviceName)
	fmt.Printf("进程信息: %s\n", processInfo)
	fmt.Printf("是否要关闭占用的进程? (y/N): ")

	reader := bufio.NewReader(os.Stdin)
	response, err := reader.ReadString('\n')
	if err != nil {
		return fmt.Errorf("读取用户输入失败: %v", err)
	}

	response = strings.TrimSpace(strings.ToLower(response))
	if response == "y" || response == "yes" {
		log.Printf("🔄 正在关闭占用端口 %d 的进程...", port)
		err := pm.KillProcessByPort(port)
		if err != nil {
			return fmt.Errorf("关闭进程失败: %v", err)
		}
		log.Printf("✅ 成功关闭占用端口 %d 的进程", port)
		return nil
	}

	return fmt.Errorf("用户选择不关闭占用端口 %d 的进程，无法启动 %s 服务", port, serviceName)
}

// CheckMultiplePorts 批量检查多个端口
func (pm *PortManager) CheckMultiplePorts(ports map[int]string, autoKill bool) error {
	var conflictPorts []int
	
	// 首先检查所有端口
	for port, serviceName := range ports {
		if pm.IsPortInUse(port) {
			conflictPorts = append(conflictPorts, port)
			log.Printf("⚠️  端口冲突: %d (%s)", port, serviceName)
		}
	}

	if len(conflictPorts) == 0 {
		return nil // 没有端口冲突
	}

	// 如果有冲突，显示所有冲突信息
	fmt.Printf("\n发现 %d 个端口冲突:\n", len(conflictPorts))
	for _, port := range conflictPorts {
		serviceName := ports[port]
		processInfo, _ := pm.GetProcessByPort(port)
		fmt.Printf("- 端口 %d (%s): %s\n", port, serviceName, processInfo)
	}

	if autoKill {
		fmt.Printf("\n🔄 自动关闭所有冲突进程...\n")
		for _, port := range conflictPorts {
			serviceName := ports[port]
			if err := pm.KillProcessByPort(port); err != nil {
				log.Printf("❌ 关闭端口 %d (%s) 的进程失败: %v", port, serviceName, err)
			} else {
				log.Printf("✅ 成功关闭端口 %d (%s) 的进程", port, serviceName)
			}
		}
		return nil
	}

	// 询问用户是否要关闭所有冲突进程
	fmt.Printf("\n是否要关闭所有冲突进程? (y/N): ")
	reader := bufio.NewReader(os.Stdin)
	response, err := reader.ReadString('\n')
	if err != nil {
		return fmt.Errorf("读取用户输入失败: %v", err)
	}

	response = strings.TrimSpace(strings.ToLower(response))
	if response == "y" || response == "yes" {
		fmt.Printf("\n🔄 正在关闭所有冲突进程...\n")
		for _, port := range conflictPorts {
			serviceName := ports[port]
			if err := pm.KillProcessByPort(port); err != nil {
				log.Printf("❌ 关闭端口 %d (%s) 的进程失败: %v", port, serviceName, err)
			} else {
				log.Printf("✅ 成功关闭端口 %d (%s) 的进程", port, serviceName)
			}
		}
		return nil
	}

	return fmt.Errorf("用户选择不关闭冲突进程，无法启动服务")
}
