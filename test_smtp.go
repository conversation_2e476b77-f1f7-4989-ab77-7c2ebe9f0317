package main

import (
	"fmt"
	"log"
	"net/smtp"
	"strings"
	"time"
)

func main() {
	// SMTP服务器配置
	smtpHost := "**************"
	smtpPort := "465"
	
	// 测试邮件内容
	from := "<EMAIL>"
	to := "<EMAIL>"
	subject := "测试邮件"
	body := "这是一封测试邮件，用于验证SMTP服务是否正常工作。\n\n发送时间: " + time.Now().Format("2006-01-02 15:04:05")
	
	// 构建邮件内容
	message := fmt.Sprintf("From: %s\r\n", from)
	message += fmt.Sprintf("To: %s\r\n", to)
	message += fmt.Sprintf("Subject: %s\r\n", subject)
	message += fmt.Sprintf("Date: %s\r\n", time.Now().Format("Mon, 02 Jan 2006 15:04:05 -0700"))
	message += "Content-Type: text/plain; charset=utf-8\r\n"
	message += "\r\n"
	message += body
	
	fmt.Printf("🧪 SMTP服务测试工具\n")
	fmt.Printf("==================\n")
	fmt.Printf("服务器: %s:%s\n", smtpHost, smtpPort)
	fmt.Printf("发件人: %s\n", from)
	fmt.Printf("收件人: %s\n", to)
	fmt.Printf("主题: %s\n", subject)
	fmt.Printf("\n")
	
	// 测试连接
	fmt.Printf("🔗 测试SMTP连接...\n")
	
	// 连接到SMTP服务器
	addr := smtpHost + ":" + smtpPort
	conn, err := smtp.Dial(addr)
	if err != nil {
		log.Fatalf("❌ 连接SMTP服务器失败: %v", err)
	}
	defer conn.Close()
	
	fmt.Printf("✅ 成功连接到SMTP服务器\n")
	
	// 发送HELO命令
	err = conn.Hello("test-client")
	if err != nil {
		log.Fatalf("❌ HELO命令失败: %v", err)
	}
	
	fmt.Printf("✅ HELO命令成功\n")
	
	// 设置发件人
	err = conn.Mail(from)
	if err != nil {
		log.Fatalf("❌ MAIL FROM命令失败: %v", err)
	}
	
	fmt.Printf("✅ MAIL FROM命令成功\n")
	
	// 设置收件人
	err = conn.Rcpt(to)
	if err != nil {
		log.Fatalf("❌ RCPT TO命令失败: %v", err)
	}
	
	fmt.Printf("✅ RCPT TO命令成功\n")
	
	// 发送邮件数据
	wc, err := conn.Data()
	if err != nil {
		log.Fatalf("❌ DATA命令失败: %v", err)
	}
	
	_, err = wc.Write([]byte(message))
	if err != nil {
		log.Fatalf("❌ 写入邮件数据失败: %v", err)
	}
	
	err = wc.Close()
	if err != nil {
		log.Fatalf("❌ 关闭数据连接失败: %v", err)
	}
	
	fmt.Printf("✅ 邮件数据发送成功\n")
	
	// 退出连接
	err = conn.Quit()
	if err != nil {
		log.Printf("⚠️ QUIT命令失败: %v", err)
	} else {
		fmt.Printf("✅ 连接正常关闭\n")
	}
	
	fmt.Printf("\n🎉 SMTP测试完成！\n")
	fmt.Printf("请检查以下内容:\n")
	fmt.Printf("1. 服务器日志中是否有邮件处理记录\n")
	fmt.Printf("2. 数据库中是否有新的邮件记录\n")
	fmt.Printf("3. API接口是否能返回新邮件\n")
	fmt.Printf("\n检查命令:\n")
	fmt.Printf("curl http://**************:8080/api/v1/emails/%s\n", to)
}

// 简化版本的测试函数
func testSMTPSimple() {
	fmt.Printf("\n🔧 简化测试模式\n")
	fmt.Printf("================\n")
	
	// 使用标准库发送邮件
	smtpHost := "**************"
	smtpPort := "465"
	
	from := "<EMAIL>"
	to := []string{"<EMAIL>"}
	
	message := []byte("From: <EMAIL>\r\n" +
		"To: <EMAIL>\r\n" +
		"Subject: 简化测试邮件\r\n" +
		"\r\n" +
		"这是一封简化的测试邮件。\r\n")
	
	addr := smtpHost + ":" + smtpPort
	
	// 不使用认证发送邮件
	err := smtp.SendMail(addr, nil, from, to, message)
	if err != nil {
		log.Printf("❌ 简化测试失败: %v", err)
	} else {
		fmt.Printf("✅ 简化测试成功\n")
	}
}
