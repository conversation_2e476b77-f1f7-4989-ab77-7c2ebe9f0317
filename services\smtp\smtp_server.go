package smtp

import (
	"fmt"
	"io"
	"log"
	"net"

	"github.com/emersion/go-smtp"
	"github.com/beego/beego/v2/server/web"
	"goemail/services/email"
)

// Backend SMTP后端实现
type Backend struct {
	emailService *email.EmailService
}

// NewBackend 创建SMTP后端
func NewBackend() *Backend {
	return &Backend{
		emailService: email.NewEmailService(),
	}
}

// NewSession 创建新的SMTP会话
func (bkd *Backend) NewSession(conn *smtp.Conn) (smtp.Session, error) {
	return &Session{
		backend: bkd,
		conn:    conn,
	}, nil
}

// Session SMTP会话实现
type Session struct {
	backend *Backend
	conn    *smtp.Conn
	from    string
	to      []string
}

// AuthPlain 处理PLAIN认证（临时邮箱不需要认证）
func (s *Session) AuthPlain(username, password string) error {
	// 临时邮箱系统不需要认证，直接返回成功
	return nil
}

// Mail 处理MAIL FROM命令
func (s *Session) Mail(from string, opts *smtp.MailOptions) error {
	log.Printf("SMTP: Mail from: %s", from)
	s.from = from
	return nil
}

// Rcpt 处理RCPT TO命令
func (s *Session) Rcpt(to string, opts *smtp.RcptOptions) error {
	log.Printf("SMTP: Rcpt to: %s", to)

	// 验证收件人地址是否属于我们的域名
	if !s.backend.emailService.IsValidDomain(to) {
		log.Printf("SMTP: 域名验证失败 - 收件人: %s, 不支持的域名", to)
		return fmt.Errorf("domain not supported: %s", to)
	}

	log.Printf("SMTP: 域名验证成功 - 收件人: %s", to)
	s.to = append(s.to, to)
	return nil
}

// Data 处理DATA命令，接收邮件内容
func (s *Session) Data(r io.Reader) error {
	log.Printf("SMTP: 开始接收邮件数据 - 发件人: %s, 收件人: %v", s.from, s.to)

	// 读取邮件内容
	emailData, err := io.ReadAll(r)
	if err != nil {
		log.Printf("SMTP: 读取邮件数据失败: %v", err)
		return fmt.Errorf("failed to read email data: %v", err)
	}

	log.Printf("SMTP: 成功读取邮件数据，大小: %d 字节", len(emailData))

	// 处理每个收件人
	for _, toAddress := range s.to {
		log.Printf("SMTP: 开始处理收件人: %s", toAddress)
		err = s.backend.emailService.ProcessIncomingEmail(emailData, toAddress)
		if err != nil {
			log.Printf("SMTP: ❌ 处理邮件失败 - 收件人: %s, 错误: %v", toAddress, err)
			// 继续处理其他收件人，不中断
		} else {
			log.Printf("SMTP: ✅ 邮件处理成功 - 收件人: %s", toAddress)
		}
	}

	log.Printf("SMTP: 邮件数据处理完成")
	return nil
}

// Reset 重置会话
func (s *Session) Reset() {
	s.from = ""
	s.to = nil
}

// Logout 登出会话
func (s *Session) Logout() error {
	return nil
}

// StartSMTPServer 启动SMTP服务器（同时监听25和465端口）
func StartSMTPServer() error {
	// 获取域名配置
	domain, err := web.AppConfig.String("mail_domain")
	if err != nil || domain == "" {
		domain = "qali.cn"
	}

	// 创建SMTP后端
	backend := NewBackend()

	// 启动端口25服务器（用于接收外部邮件）
	go func() {
		server25 := smtp.NewServer(backend)
		server25.Addr = ":25"
		server25.Domain = domain
		server25.WriteTimeout = 10 * 60
		server25.ReadTimeout = 10 * 60
		server25.MaxMessageBytes = 10 * 1024 * 1024
		server25.MaxRecipients = 50
		server25.AllowInsecureAuth = true

		log.Printf("Starting SMTP server on port 25 (incoming mail)")

		listener25, err := net.Listen("tcp", server25.Addr)
		if err != nil {
			log.Printf("Failed to listen on port 25: %v", err)
			return
		}

		if err := server25.Serve(listener25); err != nil {
			log.Printf("SMTP server on port 25 error: %v", err)
		}
	}()

	// 启动端口465服务器（用于发送邮件，SSL/TLS）
	server465 := smtp.NewServer(backend)
	server465.Addr = ":465"
	server465.Domain = domain
	server465.WriteTimeout = 10 * 60
	server465.ReadTimeout = 10 * 60
	server465.MaxMessageBytes = 10 * 1024 * 1024
	server465.MaxRecipients = 50
	server465.AllowInsecureAuth = true

	log.Printf("Starting SMTP server on port 465 (outgoing mail)")

	listener465, err := net.Listen("tcp", server465.Addr)
	if err != nil {
		return fmt.Errorf("failed to listen on port 465: %v", err)
	}

	return server465.Serve(listener465)
}

// SMTPConfig SMTP服务器配置
type SMTPConfig struct {
	Port           string
	Domain         string
	MaxMessageSize int64
	MaxRecipients  int
	WriteTimeout   int
	ReadTimeout    int
}

// GetSMTPConfig 获取SMTP配置
func GetSMTPConfig() *SMTPConfig {
	port, _ := web.AppConfig.String("smtp_port")
	if port == "" {
		port = "465"
	}
	
	domain, _ := web.AppConfig.String("mail_domain")
	if domain == "" {
		domain = "yourdomain.com"
	}
	
	return &SMTPConfig{
		Port:           port,
		Domain:         domain,
		MaxMessageSize: 10 * 1024 * 1024, // 10MB
		MaxRecipients:  50,
		WriteTimeout:   600, // 10分钟
		ReadTimeout:    600, // 10分钟
	}
}
